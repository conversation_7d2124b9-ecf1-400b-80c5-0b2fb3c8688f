package com.tencent.supersonic.headless.chat.query.llm.s2sql;

import com.fasterxml.jackson.annotation.JsonValue;
import com.google.common.collect.Lists;
import com.tencent.supersonic.common.pojo.ChatApp;
import com.tencent.supersonic.common.pojo.Text2SQLExemplar;
import com.tencent.supersonic.headless.api.pojo.SchemaElement;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * LLMReq 是大语言模型(LLM)进行自然语言转SQL(Text2SQL)任务的请求对象
 *
 * 该类封装了LLM生成SQL所需的所有上下文信息，包括：
 * - 用户的自然语言查询文本
 * - 数据库模式信息（表结构、字段、类型等）
 * - 业务术语和别名映射
 * - 时间上下文和历史信息
 * - 生成策略配置
 * - 动态示例和提示词配置
 *
 * 该类是Supersonic系统中LLM解析器的核心数据结构，用于在LLMSqlParser中
 * 构建请求并传递给不同的SQL生成策略（如OnePassSCSqlGenStrategy）进行处理。
 *
 * <AUTHOR> Team
 * @see com.tencent.supersonic.headless.chat.parser.llm.LLMSqlParser
 * @see com.tencent.supersonic.headless.chat.parser.llm.OnePassSCSqlGenStrategy
 * @see com.tencent.supersonic.headless.chat.parser.llm.LLMRequestService
 */
@Data
public class LLMReq {

    /** 查询的唯一标识ID */
    private Long queryId;

    /** 用户输入的自然语言查询文本，这是需要转换为SQL的原始问题 */
    private String queryText;

    /** 数据库模式信息，包含表结构、字段定义、数据类型等LLM生成SQL所需的元数据 */
    private LLMSchema schema;

    /** 业务术语列表，用于帮助LLM理解查询中的业务概念和专业术语 */
    private List<Term> terms;

    /** 当前日期，为LLM提供时间上下文，用于处理相对时间查询（如"昨天"、"本月"等） */
    private String currentDate;

    /** 先验扩展信息，包含额外的上下文信息用于辅助SQL生成 */
    private String priorExts;

    /** SQL生成策略类型，决定使用哪种LLM提示策略来生成SQL */
    private SqlGenType sqlGenType;

    /** 聊天应用配置映射，包含不同模块的LLM配置信息（模型参数、提示词模板等） */
    private Map<String, ChatApp> chatAppConfig;

    /** 自定义提示词，允许用户或系统提供特定的提示词来指导SQL生成 */
    private String customPrompt;

    /** 动态示例列表，包含相似查询的Text2SQL示例，用于few-shot学习提升生成质量 */
    private List<Text2SQLExemplar> dynamicExemplars;

    /**
     * ElementValue 表示数据库字段的具体取值
     *
     * 在Text2SQL转换过程中，用于存储维度字段的具体值，
     * 这些值通常来自用户查询中提到的具体实体或条件。
     * 例如：用户问"北京地区的销售额"，则fieldName为"地区"，fieldValue为"北京"
     */
    @Data
    public static class ElementValue {
        /** 字段名称，对应数据库表中的列名 */
        private String fieldName;

        /** 字段的具体取值，用于WHERE条件中的过滤 */
        private String fieldValue;
    }

    /**
     * LLMSchema 封装了LLM生成SQL所需的完整数据库模式信息
     *
     * 该类包含了数据集的元数据信息，为LLM提供了理解数据结构的上下文，
     * 包括数据库类型、表结构、字段定义、主键、分区字段等信息。
     * 这些信息帮助LLM生成符合特定数据库语法和业务逻辑的SQL语句。
     */
    @Data
    public static class LLMSchema {
        /** 数据库类型（如MySQL、PostgreSQL、H2等），用于生成特定数据库语法的SQL */
        private String databaseType;

        /** 数据库版本信息，用于处理不同版本间的语法差异 */
        private String databaseVersion;

        /** 数据集的唯一标识ID */
        private Long dataSetId;

        /** 数据集的名称，通常对应数据库中的表名 */
        private String dataSetName;

        /** 指标列表，包含可聚合的数值型字段（如销售额、用户数等） */
        private List<SchemaElement> metrics;

        /** 维度列表，包含用于分组和筛选的字段（如地区、时间、用户类型等） */
        private List<SchemaElement> dimensions;

        /** 字段值列表，包含维度字段的具体取值，用于WHERE条件构建 */
        private List<ElementValue> values;

        /** 分区时间字段，用于时间范围查询和性能优化 */
        private SchemaElement partitionTime;

        /** 主键字段，用于唯一标识记录 */
        private SchemaElement primaryKey;

        /**
         * 获取所有字段名称的列表
         *
         * 该方法汇总了Schema中所有类型字段的名称，包括：
         * - 指标字段名称
         * - 维度字段名称
         * - 值字段名称
         * - 分区时间字段名称
         *
         * 使用Set去重确保字段名称的唯一性，最后转换为List返回。
         * 这个方法通常用于构建LLM的提示词，让模型了解可用的字段。
         *
         * @return 去重后的字段名称列表
         */
        public List<String> getFieldNameList() {
            Set<String> fieldNameList = new HashSet<>();

            // 添加所有指标字段名称
            if (CollectionUtils.isNotEmpty(metrics)) {
                fieldNameList.addAll(
                        metrics.stream().map(SchemaElement::getName).collect(Collectors.toList()));
            }

            // 添加所有维度字段名称
            if (CollectionUtils.isNotEmpty(dimensions)) {
                fieldNameList.addAll(dimensions.stream().map(SchemaElement::getName)
                        .collect(Collectors.toList()));
            }

            // 添加所有值字段名称
            if (CollectionUtils.isNotEmpty(values)) {
                fieldNameList.addAll(values.stream().map(ElementValue::getFieldName)
                        .collect(Collectors.toList()));
            }

            // 添加分区时间字段名称
            if (Objects.nonNull(partitionTime)) {
                fieldNameList.add(partitionTime.getName());
            }

            return new ArrayList<>(fieldNameList);
        }
    }

    /**
     * Term 表示业务术语或领域概念
     *
     * 在Text2SQL转换过程中，Term用于帮助LLM理解用户查询中的业务术语，
     * 将自然语言中的业务概念映射到具体的数据库字段或计算逻辑。
     * 例如："核心用户"这个术语可能对应特定的用户筛选条件。
     */
    @Data
    public static class Term {
        /** 术语名称，业务概念的标准名称 */
        private String name;

        /** 术语描述，详细解释该术语的含义和使用场景 */
        private String description;

        /** 术语的别名列表，包含该术语的各种表达方式和同义词 */
        private List<String> alias = Lists.newArrayList();
    }

    /**
     * SqlGenType 定义了SQL生成策略的类型
     *
     * 该枚举用于指定LLM生成SQL时采用的策略和方法，
     * 不同的策略会影响提示词构建、推理过程和结果质量。
     */
    public enum SqlGenType {
        /**
         * 一次性自一致性策略
         *
         * 该策略使用自一致性(Self-Consistency)方法来提高SQL生成的准确性。
         * 通过多次推理并选择最一致的结果，减少随机性带来的错误。
         * 适用于对准确性要求较高的场景。
         */
        ONE_PASS_SELF_CONSISTENCY("1_pass_self_consistency");

        /** 策略的字符串标识名称 */
        private final String name;

        /**
         * 构造函数
         *
         * @param name 策略的字符串标识名称
         */
        SqlGenType(String name) {
            this.name = name;
        }

        /**
         * 获取策略的字符串标识名称
         *
         * @JsonValue 注解确保在JSON序列化时使用该方法的返回值
         * @return 策略的字符串标识名称
         */
        @JsonValue
        public String getName() {
            return name;
        }
    }
}
